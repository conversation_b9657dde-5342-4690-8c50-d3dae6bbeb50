.management-container {
  display: flex;
  flex-direction: column;

  .ant-tabs-content {
    height: calc(100% - 5px);

    .ant-tabs-tabpane {
      height: 100%;
    }
  }

  .batch-label {
    font-size: 14px;
    color: #7d87b3;
  }

  .ant-tabs {
    margin-top: 8px;
    background: #ffffff;
    .ant-tabs-content-holder {
      margin-bottom: 16px;
    }
    .ant-tabs-nav {
      padding: 0px 20px;
      margin: 0px;
    }
  }

  .title-label {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px 20px;
  }
}

.management-search-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .ant-card-body {
    display: flex;
    flex-direction: row;
    width: 100%;
  }

  .search-form-container {
    display: flex;
    flex-direction: row;
    width: 100%;

    .ant-form-item {
      margin-bottom: 0px;
    }
  }
}

.review-person-summary-container {
  .stats-card {
    display: flex;
    overflow-x: auto;
    .ant-pro-checkcard {
      flex: 1 1 0;
      min-width: 140px;
      margin-block-end: 0;
      margin-inline-end: 8px;
    }
  }

  .edit-btn {
    float: right;
    margin-bottom: 8px;
  }
  .ant-card-body {
    padding: 5px;
    height: 100%;
  }

  .ant-card-head {
    align-items: center;
    display: flex;
    flex-direction: row;
    padding: 0px 6px;
  }

  .ant-card-head-title {
    padding: 4px 0px;
    font-size: 16px;
  }

  thead .ant-table-cell {
    white-space: nowrap;
  }
  .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-body
    > table
    > tbody
    > tr
    > td {
    width: 150px;
  }
}

.review-info-container {
  display: flex;
  flex-direction: row;
  margin: 5px 10px;
  height: calc(100% - 10px);

  .info-search-container {
    display: flex;
    width: 500px;
    flex-direction: column;
    align-items: center;
    padding: 20px 10px 10px 10px;
    border-right: 1px solid #f0f0f0;

    .ant-form-item {
      margin-bottom: 14px;
      label {
        color: #7d87b3;
        white-space: nowrap;
        min-width: 70px;
        width: 70px;
        justify-content: flex-end;
      }
    }

    .search-btn {
      width: calc(100% - 20px);
    }
  }
}

.progress-outer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-label {
  display: none;
}

.progress-container {
  position: relative;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  vertical-align: middle;
  background-color: #0000000a;
  border-radius: 100px;
  height: 8px;

  .progress-item {
    position: absolute;
    top: 0;
    height: 8px;
    transition: all 0.4s cubic-bezier(0.08, 0.82, 0.17, 1) 0s;
  }

  .progress-item:first-child {
    border-top-left-radius: 100px;
    border-bottom-left-radius: 100px;
  }

  .progress-item:last-child {
    border-top-right-radius: 100px;
    border-bottom-right-radius: 100px;
  }
}
