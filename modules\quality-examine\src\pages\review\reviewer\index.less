.reviewer-container {
  display: flex;
  flex-direction: column;

  .header-content-container {
  }

  .batch-add-dmr {
    color: #1890ff;
    font-weight: bold;
    cursor: pointer;
  }
}

.stats-container {
  margin-bottom: 8px;
  .ant-spin-nested-loading,
  .ant-spin-container,
  .single-stat-card {
    height: 100%;
  }

  .single-stat-card {
    padding: 10px 0 0 10px;
  }
}
.review-person-summary-container {
  .stats-card {
    display: flex;
    overflow-x: auto;
    .ant-pro-checkcard {
      flex: 1 1 0;
      min-width: 140px;
      margin-block-end: 0;
      margin-inline-end: 8px;
    }
  }
  .edit-btn {
    float: right;
    margin-bottom: 8px;
  }
}
