import React, { useEffect } from 'react';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { Dispatch } from '@@/plugin-dva/connect';
import { useAccess } from '@@/plugin-access/access';
import { useAsyncEffect } from 'ahooks';
import { dictModuleInitialize } from '@/utils/initialize';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import merge from 'lodash/merge';
import { useModel } from 'umi';
import { v4 as uuidv4 } from 'uuid';
import { useLocation } from 'umi';
import { history } from '@@/core/history';
import { notification } from 'antd';
import dayjs from 'dayjs';
import useTabTracker from '@uni/utils/src/tab-tracker';
import { doLog } from '@uni/services/src';
import { LogLevel } from '@uni/services/src/commonService';

// 当前标签页的ID
global['clientId'] = uuidv4();

const clearTokenOnClose =
  (window as any).externalConfig?.['common']?.clearTokenOnClose ?? false;
const clearTokenOnNextDay =
  (window as any).externalConfig?.['common']?.clearTokenOnNextDay ?? false;

export default function InitLayout(props: any) {
  const { initialState, setInitialState, refresh }: any =
    useModel('@@initialState');

  const dvaDictData = useSelector((state: any) => state.uniDict.dictData);

  const dispatch: Dispatch = useDispatch();

  const access = useAccess();

  const location = useLocation();

  let tabAwareWorker;

  // 用户信息给到 子应用
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  if (clearTokenOnClose) {
    useTabTracker(global['clientId']);
  }

  useAsyncEffect(async () => {
    await dictModuleInitialize(dispatch);

    initializeTabAwareWorker();

    if (clearTokenOnNextDay === true) {
      // 这边是处理：当某些不可控的特殊情况 导致在没关闭浏览器时直接关机 这种时候可能会无法触发 beforeunload
      // 当前时间的年月日 !== 登陆时记录时间的年月日 则清除localstorage *强制机制
      // 当然 这个最好的方式是后端处理 但是他们不处理
      let connectTimestamp = localStorage.getItem('uni-connect-timestamp');
      if (
        connectTimestamp &&
        !dayjs().isSame(dayjs(+connectTimestamp), 'day')
      ) {
        localStorage.clear();
        console.log('hhhh');
        doLog(LogLevel.Trace, JSON.stringify({ title: '第二天强制登出' }));
        global?.window.location.replace('/login');
      }
    }
  }, []);

  useEffect(() => {
    if (global['tabAwareWorker']) {
      global['tabAwareWorker'].port.postMessage({
        type: 'ROUTE_CHANGE',
        clientId: global['clientId'],
        location: location?.pathname,
      });
    }
  }, [location]);

  const initializeTabAwareWorker = () => {
    tabAwareWorker = new SharedWorker('/tabAwareWorker.js', 'tab-aware-worker');
    tabAwareWorker.port.postMessage({
      type: 'TAB_CONNECT',
      clientId: global['clientId'],
      location: location?.pathname,
    });
    tabAwareWorker.port.onmessage = (event) => {
      if (event?.data?.type === 'CLEAR_TOKEN_ON_CLOSE') {
        global['CLEAR_TOKEN_ON_CLOSE'] = event?.data?.payload;
      }

      if (event?.data?.type === 'ROUTE_REDIRECT') {
        if (event?.data?.url) {
          history.replace(event?.data?.url);
        }
      }

      if (event?.data?.type === 'NEW_WINDOW') {
        if (event?.data?.url) {
          // window.open(event?.data?.url, "", `popup=1,height=${window.screen.height},width=${window.screen.width}`)
          window.open(event?.data?.url);
        }
      }

      if (event?.data?.type === 'EXIST_WINDOW') {
        // 通知
        // TODO 通知的文案
        notification.info({
          ...event?.data?.payload,
          getContainer: () => document.getElementById('root-container'),
        });
      }
    };

    global['tabAwareWorker'] = tabAwareWorker;
  };

  useEffect(() => {
    Emitter.emit(EventConstant.DICT_DATA_CHANGE, dvaDictData);
  }, [dvaDictData]);

  useEffect(() => {
    Emitter.on(EventConstant.DICT_DATA_CHANGE, (dvaDictData) => {
      let dictData = merge({}, globalState?.dictData, dvaDictData);

      setQiankunGlobalState({
        ...globalState,
        // 若因为dict change 而set的globalState searchParams的 triggerSource 变成 other,保证不会污染通过btnClick判断要不要调接口的useEffect
        searchParams: { ...globalState?.searchParams, triggerSource: 'other' },
        dictData: dictData,
        // 放一份 access 和 userInfo 和 chsConfigurationInfo
        userInfo: initialState?.userInfo,
        access: access,
        chsConfigurationInfo: initialState?.configurationInfo,
      });
    });

    return () => {
      Emitter.off(EventConstant.DICT_DATA_CHANGE);
    };
  }, [globalState, access, initialState]);

  return <>{props.children}</>;
}
