import React, { useState, useRef, useEffect } from 'react';
import {
  Layout,
  Menu,
  notification,
  Form,
  Input,
  Dropdown,
  Badge,
  Space,
  Button,
  message,
  List,
  Tooltip,
  Modal,
  TreeSelect,
} from 'antd';
import {
  UserOutlined,
  NotificationOutlined,
  UnlockOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LogoutOutlined,
  HomeOutlined,
  ChromeOutlined,
  MailOutlined,
  SwitcherOutlined,
} from '@ant-design/icons';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { HeaderMenuItem, MenuData } from '../menu-sider/MenuSider';
import intersection from 'lodash/intersection';
import { useRequest } from 'ahooks';
import { uniCommonService, doLog } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { getUserInfo } from '@uni/services/src/userService';
import cloneDeep from 'lodash/cloneDeep';
import {
  getAllLeafMenuItem,
  headerMenuKeysToHeaderIds,
  isEmptyValues,
} from '@uni/utils/src/utils';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import DocumentFolder from '@uni/commons/src/icon/DocumentFolder';
import PersonalMessagesHeaderBtn from '../personalMessageBtn';
import uniq from 'lodash/uniq';
import { LogLevel } from '@uni/services/src/commonService';

const { Header } = Layout;

const headerNewTab =
  (window as any).externalConfig?.['common']?.headerNewTab ?? false;
const chromeDownload =
  (window as any).externalConfig?.['common']?.chromeDownload ?? false;
const wikiShow = (window as any).externalConfig?.['common']?.wikiShow ?? false;
const wikiInsurShow =
  (window as any).externalConfig?.['common']?.wikiInsurShow ?? false;
const personalMessageShow =
  (window as any).externalConfig?.['common']?.personalMessageShow ?? false;

const externalLogo = (window as any).externalConfig?.['common']?.externalLogo;

export const logout = () => {
  doLog(
    LogLevel.Trace,
    JSON.stringify({
      title: '用户主动注销',
      userInfo: JSON.parse(sessionStorage?.getItem('userInfo') || '{}'),
    }),
  );
  global?.window.location.replace('/login');
  localStorage.removeItem('uni-connect-token');
  localStorage.removeItem('expireItem');
  localStorage.removeItem('uni-connect-timestamp');
  message.success('注销成功。');
};

export const mainPage = (url?: string) => {
  global?.window.location.replace(url || '/main');
};

export const wiki = () => {
  // 防止 现在在wiki下再打开一个wiki
  if (global?.window?.location?.pathname?.indexOf('/wiki') > -1) {
    return;
  }
  global?.window?.open(wikiInsurShow ? '/wiki/insur/index' : '/wiki/dmr/icde');
};

interface SiteHeaderProps {
  access: any;
  menuData: MenuData[];
  headerMenu: HeaderMenuItem[];
  homeUrl?: string;

  menuOrder?: any[];

  userInfo?: any;
}

const SiteHeader = (props: SiteHeaderProps) => {
  const [headerMenuSelectedKey, setHeaderMenuSelectedKey] = useState(undefined);

  const [accessHeaderMenuKeys, setAccessHeaderMenuKeys] = useState<string[]>(
    [],
  );
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalInfo, setModalInfo] = useState<any>({});

  const [homeUrl, setHomeUrl] = useState('');

  const [menuOrder, setMenuOrder] = useState<any[]>([]);

  useEffect(() => {
    let hasAccessUrls = Object.keys(props?.access || {})?.filter(
      (item) => props?.access[item] === true,
    );
    // console.log('siteHeader hasAccessUrls', hasAccessUrls);
    const accessHeaderMenuKeys = uniq(
      props?.menuData
        ?.filter((item) => hasAccessUrls.includes(item?.route))
        ?.map((item) => item?.headerMenuKey)
        ?.filter((item) => item),
    );
    // console.log('siteHeader accessHeaderMenuKeys', accessHeaderMenuKeys);

    setAccessHeaderMenuKeys(accessHeaderMenuKeys);
  }, [props?.access]);

  useEffect(() => {
    setMenuOrder(props?.menuOrder);
  }, [props?.menuOrder]);

  useEffect(() => {
    Emitter.on('HEADER_MENU_SELECT_BY_LOCATION', (data) => {
      setHeaderMenuSelectedKey(data?.rootPath);
    });

    (global?.window as any)?.eventEmitter?.on(
      'HEADER_MENU_ORDER_CHANGE',
      (menuOrder) => {
        setMenuOrder(menuOrder);
      },
    );

    // 通知loaded
    Emitter.emit('HEADER_MENU_LOADED');

    return () => {
      Emitter.off('HEADER_MENU_SELECT_BY_LOCATION');
      (global?.window as any)?.eventEmitter?.off('HEADER_MENU_ORDER_CHANGE');
    };
  }, []);

  // 用于在其他地方暴露headerMenu
  useEffect(() => {
    (global?.window as any)?.eventEmitter.on('HEADER_MENU_DATA_REQUEST', () => {
      (global?.window as any)?.eventEmitter.emit(
        'HEADER_MENU_DATA_RESPONSE',
        cloneDeep(props?.headerMenu),
      );
    });

    return () => {
      (global?.window as any)?.eventEmitter.off('HEADER_MENU_DATA_REQUEST');
    };
  }, [props?.headerMenu]);

  useEffect(() => {
    setHomeUrl(props?.homeUrl);
  }, [props?.homeUrl]);

  const changePassword = () => {
    setModalVisible(true);
    setModalInfo({
      key: 'password',
      title: '修改密码',
      api: 'Api/Account/User/ChangePassword',
    });
  };

  const changeProfile = () => {
    setModalVisible(true);
    setModalInfo({
      key: 'profile',
      title: '修改资料',
      api: 'Api/Account/User/EditProfile',
    });
  };

  const avatarMenu = (
    <Menu>
      <Menu.Item key="changeProfile" onClick={changeProfile}>
        <span>
          <UserOutlined /> 修改资料
        </span>
      </Menu.Item>
      <Menu.Item key="changePassword" onClick={changePassword}>
        <span>
          <UnlockOutlined /> 修改密码
        </span>
      </Menu.Item>
      <Menu.Item key="logout" onClick={logout}>
        <span>
          <LogoutOutlined /> 退出登录
        </span>
      </Menu.Item>
    </Menu>
  );

  const onHeaderMenuItemClick = async (item) => {
    if (headerNewTab) {
      if (item?.route?.startsWith('Api/')) {
        // 表示是接口 需要请求一下数据
        let drgActualUrlResp: RespVO<string> = await uniCommonService(
          item?.route,
          { method: 'POST' },
        );
        let content = await drgActualUrlResp?.response?.text();
        if (
          drgActualUrlResp?.code === 0 &&
          drgActualUrlResp?.statusCode === 200
        ) {
          window.open(content);
        } else {
          message.error('院内版跳转出现问题，请联系开发人员');
        }
      } else {
        // 有权限
        if (props?.access[item?.headerMenuItemActualRoute]) {
          window.open(item?.headerMenuItemActualRoute);
        } else {
          // 没有权限 跳转到 access中存在的当前菜单的第一个
          let hasAccessFirstRoute = findFirstAccessRouteInCurrentMenuData(
            item?.route,
          );
          if (hasAccessFirstRoute) {
            window.open(hasAccessFirstRoute);
          }
        }
      }
    } else {
      if (item?.route?.startsWith('http://')) {
        window.open(item?.route);
      } else if (item?.route?.startsWith('Api/')) {
        // 表示是接口 需要请求一下数据
        let drgActualUrlResp: RespVO<string> = await uniCommonService(
          item?.route,
          { method: 'POST' },
        );
        let content = await drgActualUrlResp?.response?.text();
        if (
          drgActualUrlResp?.code === 0 &&
          drgActualUrlResp?.statusCode === 200
        ) {
          window.open(content);
        } else {
          message.error('院内版跳转出现问题，请联系开发人员');
        }
      } else {
        let headerMenuItem = cloneDeep(item);
        if (props?.access[headerMenuItem?.headerMenuItemActualRoute]) {
          Emitter.emit('HEADER_MENU_SELECT', headerMenuItem);
        } else {
          // 没有权限 跳转到 access中存在的当前菜单的第一个
          let hasAccessFirstRoute = findFirstAccessRouteInCurrentMenuData(
            item?.route,
          );
          if (hasAccessFirstRoute) {
            headerMenuItem['headerMenuItemActualRoute'] = hasAccessFirstRoute;
            Emitter.emit('HEADER_MENU_SELECT', headerMenuItem);
          }
        }

        setHeaderMenuSelectedKey(headerMenuItem?.route);
        Emitter.emit(EventConstant.TOP_MENU_CLICK);
      }
    }
  };

  const { run: editUsersReq } = useRequest(
    (data) => {
      return uniCommonService(modalInfo?.api, {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      // formatResult: (response: RespVO<any>) => {
      //   return response;
      // },
      onSuccess: (response, params) => {
        if (response?.statusCode === 200) {
          message.success('修改成功');
          setModalVisible(false);
        }
      },
    },
  );

  const findFirstAccessRouteInCurrentMenuData = (headerRoute: string) => {
    let parentRouteLeafItems = getAllLeafMenuItem(props?.menuData, headerRoute);

    return parentRouteLeafItems?.find((item) => props?.access[item]);
  };

  const headerItemIds = headerMenuKeysToHeaderIds(
    accessHeaderMenuKeys,
    props?.headerMenu,
  );

  return (
    <>
      <Header className="site-layout-header">
        <div className="site-layout-header-mask d-flex">
          <div className={'header-center header-menu'}>
            {headerItemIds?.length > 0 && (
              <Menu selectedKeys={[headerMenuSelectedKey]} mode="horizontal">
                {props?.headerMenu
                  ?.map((item) => {
                    let currentMenuItem = cloneDeep(item);
                    if (!isEmptyValues(menuOrder)) {
                      let currentHeaderMenuOrder = menuOrder?.findIndex(
                        (remoteMenuItem) =>
                          remoteMenuItem?.id === currentMenuItem?.id,
                      );
                      currentMenuItem['order'] =
                        currentHeaderMenuOrder === -1
                          ? item?.order ?? 65535
                          : currentHeaderMenuOrder;
                      currentMenuItem['childrenOrder'] =
                        menuOrder?.[currentHeaderMenuOrder]?.children;
                    }
                    return currentMenuItem;
                  })
                  ?.sort((a, b) => (a?.order ?? 65535) - (b?.order ?? 65535))
                  ?.filter(
                    (item) =>
                      intersection(accessHeaderMenuKeys, item?.key)?.length > 0,
                  )
                  ?.map((headerMenuItem) => {
                    return (
                      <Menu.SubMenu
                        key={headerMenuItem?.key?.join(',')}
                        title={
                          <>
                            <img
                              src={require(`@/assets/main/${headerMenuItem?.icon}`)}
                            />
                            {headerMenuItem?.name}
                          </>
                        }
                      >
                        {props?.menuData
                          ?.map((item) => {
                            if (!isEmptyValues(headerMenuItem?.childrenOrder)) {
                              let index =
                                headerMenuItem?.childrenOrder?.findIndex(
                                  (remoteMenuItem) =>
                                    remoteMenuItem?.id === item?.route,
                                );
                              item['order'] =
                                index === -1 ? item?.order ?? 65535 : index;
                              item['customTitle'] =
                                headerMenuItem?.childrenOrder?.[
                                  index
                                ]?.customTitle;
                            }
                            return item;
                          })
                          ?.sort(
                            (a, b) => (a?.order ?? 65535) - (b?.order ?? 65535),
                          )
                          ?.filter((item) => props?.access[item.route])
                          ?.filter((item) =>
                            headerMenuItem?.key?.includes(item?.headerMenuKey),
                          )
                          ?.map((menuItem) => {
                            return (
                              <Menu.Item
                                key={menuItem?.route}
                                onClick={() => onHeaderMenuItemClick(menuItem)}
                              >
                                {menuItem?.customTitle || menuItem?.name}
                              </Menu.Item>
                            );
                          })}
                      </Menu.SubMenu>
                    );
                  })}
              </Menu>
            )}
          </div>
          <div className={'header-left'}></div>
          <div className="header-right">
            <Space size="middle">
              {props?.userInfo && (
                <div className={'username'}>
                  <span>当前用户：</span>
                  <span>{props?.userInfo?.Name}</span>
                </div>
              )}
              {personalMessageShow && <PersonalMessagesHeaderBtn />}
              {wikiShow && (
                <Tooltip title={'知识库'}>
                  <DocumentFolder
                    className={'icon'}
                    onClick={() => {
                      wiki();
                    }}
                  />
                </Tooltip>
              )}
              <Tooltip title={'回到首页'}>
                <HomeOutlined
                  className={'icon'}
                  onClick={() => {
                    mainPage(homeUrl);
                  }}
                />
              </Tooltip>
              {chromeDownload && (
                <Tooltip title={'下载新版浏览器'}>
                  <ChromeOutlined
                    className={'icon'}
                    onClick={() => {
                      // TODO 浏览器地址 修改
                      window.open('/ChromeSetup64.exe');
                    }}
                  />
                </Tooltip>
              )}
              <Dropdown overlay={avatarMenu} trigger={['click']}>
                <UserOutlined />
              </Dropdown>
              <LogoutOutlined className={'icon'} onClick={logout} />

              {!isEmptyValues(externalLogo) && (
                <img className={'external-logo'} src={`/${externalLogo}`} />
              )}
            </Space>
          </div>
        </div>
      </Header>

      <Modal
        title={modalInfo?.title}
        open={modalVisible}
        onOk={() => {
          editUsersReq(form.getFieldsValue());
          if (modalInfo?.key === 'profile') {
            if (form.getFieldValue('UserId')) {
              //更新一下用户的落地页
              let data = {
                IdentityCode: form.getFieldValue('UserId'),
                IdentityType: 'User',
                ConfigModule: 'HomePage',
                FullReplace: true,
                values: {
                  Default: form.getFieldValue('HomePage') ?? '/main',
                },
              };

              uniCommonService('Api/Sys/ClientKitSys/SetValue', {
                method: 'POST',
                requestType: 'json',
                data: data,
              });

              setHomeUrl(form.getFieldValue('HomePage'));
            }
          }
        }}
        onCancel={() => {
          setModalVisible(false);
        }}
        destroyOnClose={true}
        okButtonProps={{ htmlType: 'submit' }}
      >
        {modalInfo?.key === 'password' && <UserChangePassword form={form} />}
        {modalInfo?.key === 'profile' && (
          <UserChangeProfile
            form={form}
            access={props?.access}
            menuData={props?.menuData}
            homeUrl={homeUrl}
          />
        )}
      </Modal>
    </>
  );
};

export const UserChangePassword = (props) => {
  return (
    <div className={'user-change-pwd-container'}>
      <Form
        form={props?.form}
        preserve={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <Form.Item
          label="旧密码"
          name={'OldPassword'}
          rules={[{ required: true }]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="新密码"
          name={'NewPassword'}
          rules={[{ required: true }]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="确认密码"
          name={'ConfirmPassword'}
          rules={[{ required: true }]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>
      </Form>
    </div>
  );
};

const getUserProfile = async () => {
  let response: any = await getUserInfo();
  if (response?.code === 0) {
    if (response?.statusCode === 200) {
      return response?.data;
    }
  }
  return {};
};

const setUserInfo = async (props) => {
  let userInfo = await getUserProfile();
  props?.form.setFieldsValue(userInfo);
};

export const UserChangeProfile = (props) => {
  setUserInfo(props);
  props?.form.setFieldValue('HomePage', props?.homeUrl);

  const filterData = (data) =>
    data.filter((o) => {
      if (o.children) {
        o.selectable = false;
        o.children = filterData(o.children);
      }
      if (isEmptyValues(props?.access)) {
        return true;
      } else {
        return props?.access[o.route] === true;
      }
    });

  return (
    <div className={'user-change-profile-container'}>
      <Form
        form={props?.form}
        preserve={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <Form.Item label="用户ID" name={'UserId'} hidden={true} />

        <Form.Item label="姓名" name={'Name'}>
          <Input />
        </Form.Item>
        <Form.Item label="电话" name={'PhoneNumber'}>
          <Input />
        </Form.Item>
        <Form.Item label="微信" name={'WeChat'}>
          <Input />
        </Form.Item>
        <Form.Item label="落地页" name={'HomePage'}>
          <TreeSelect
            allowClear={true}
            fieldNames={{ label: 'name', value: 'route' }}
            showSearch={true}
            treeData={[
              {
                name: '卡片首页',
                route: '/main',
              },
            ].concat(filterData(props?.menuData))}
            treeCheckable={false}
            placeholder={'请选择'}
            style={{
              width: '100%',
            }}
            filterTreeNode={(inputValue, treeNode) => {
              // console.log('treeNode', treeNode);
              return (
                treeNode?.name?.indexOf(inputValue?.toLowerCase()) > -1 ||
                treeNode?.route?.indexOf(inputValue?.toLowerCase()) > -1 ||
                pinyinInitialSearch(treeNode?.name, inputValue)
              );
            }}
            onChange={(value, label, extra) => {
              props?.form?.setFieldValue('HomePage', value);
            }}
          />
        </Form.Item>
      </Form>
    </div>
  );
};
export default SiteHeader;
